# ThreatMesh Database Schema

This document describes the database schema used by the ThreatMesh API.

## Overview

ThreatMesh uses MongoDB as its primary database. The database stores information about tasks, their results, and the relationships between them. The database uses a hybrid approach where main operation metadata is stored in the `operations` collection, while detailed results for certain features are stored in separate collections using a chunk-based approach for better performance and scalability.

## Collections

### Operations Collection

The `operations` collection is the main collection that stores information about tasks and their results. It replaces the separate `tasks` and `results` collections in a more unified approach.

#### Schema

```json
{
  "_id": "6071f1234567890abcdef123",  // UUID string
  "feature": "subdomain_enumeration",  // Feature name
  "parameters": {
    "domain": "example.com"  // Feature-specific parameters
  },
  "status": "completed",  // Task status (pending, running, completed, failed, killed, suspended)
  "created_at": "2023-04-10T12:34:56.789Z",  // Creation timestamp
  "updated_at": "2023-04-10T12:35:01.234Z",  // Last update timestamp
  "start_time": "2023-04-10T12:34:56.789Z",  // When task started running
  "end_time": "2023-04-10T12:35:01.234Z",  // When task completed or failed
  "error": null,  // Error message if status is "failed"
  "progress_perc": 100,  // Progress percentage (0-100)
  "task_id": "celery-task-id-123",  // Celery task ID
  "raw_output": [  // Raw command output or API responses (only stored for failed tasks)
    "Line 1 of output",
    "Line 2 of output"
  ],
  "structured_output": {  // Processed and structured results
    "subdomains": [
      "api.example.com",
      "www.example.com",
      "mail.example.com"
    ],
    "sources": {
      "crtsh": 2,
      "alienvault": 1,
      "hackertarget": 1
    },
    "count": 3
  }
}
```


### Pastes Search Collection

The `pastes_search` collection stores detailed pastes search results in chunks.

#### Schema

```json
{
  "_id": "ObjectId",
  "operation_id": "6071f1234567890abcdef123",  // Reference to operations collection
  "domain": "example.com",  // Domain being searched
  "chunk_number": 1,  // Chunk number for pagination
  "created_at": "2023-04-10T12:35:01.234Z",  // Creation timestamp
  "count": 50,  // Number of records in this chunk
  "data": [  // Array of paste records
    {
      "paste_id": "abc123",
      "title": "Config dump",
      "url": "https://pastebin.com/abc123",
      "date": "2023-04-10",
      "content_preview": "database_host=example.com...",
      "source": "pastebin"
    }
  ]
}
```

### Git Search Collection

The `git_search` collection stores detailed git search results in chunks.

#### Schema

```json
{
  "_id": "ObjectId",
  "operation_id": "6071f1234567890abcdef123",  // Reference to operations collection
  "chunk_number": 1,  // Chunk number for pagination
  "created_at": "2023-04-10T12:35:01.234Z",  // Creation timestamp
  "count": 25,  // Number of records in this chunk
  "data": [  // Array of git search records
    {
      "repo": "user/repository",
      "file_path": "config/database.yml",
      "rule_id": "aws",
      "match": "aws_secret_access_key=AKIA...",
      "line_number": 15,
      "commit_hash": "abc123def456",
      "url": "https://github.com/user/repository/blob/main/config/database.yml#L15"
    }
  ]
}
```

### Darkweb Leaks Collection

The `darkweb_leaks` collection stores detailed darkweb leaks results in chunks.

#### Schema

```json
{
  "_id": "ObjectId",
  "operation_id": "6071f1234567890abcdef123",  // Reference to operations collection
  "domain": "example.com",  // Domain being searched
  "chunk_number": 1,  // Chunk number for pagination
  "created_at": "2023-04-10T12:35:01.234Z",  // Creation timestamp
  "count": 75,  // Number of records in this chunk
  "data": [  // Array of darkweb leak records
    {
      "database_name": "company_db_2023",
      "leak_date": "2023-04-01",
      "record_count": 50000,
      "description": "Employee database leak",
      "source": "darkweb_forum_xyz",
      "sample_data": {
        "emails": ["<EMAIL>", "<EMAIL>"],
        "fields": ["email", "password_hash", "phone"]
      }
    }
  ]
}
```

### webserver_discovery Collection

The `webserver_discovery` collection stores detailed webserver discovery results in chunks.

#### Schema

```json
{
  "_id": "ObjectId",
  "operation_id": "6071f1234567890abcdef123",  // Reference to operations collection
  "domain": "example.com",  // Domain being scanned
  "chunk_number": 1,  // Chunk number for pagination
  "created_at": "2023-04-10T12:35:01.234Z",  // Creation timestamp
  "count": 50,  // Number of records in this chunk
  "data": [  // Array of webserver records
    {
      "url": "https://api.example.com",
      "location": "https://api.example.com",
      "title": "API Server",
      "webserver": "nginx/1.18.0",
      "content_type": "application/json",
      "response_time": "1.5",
      "a_records": ["************0"],
      "aaaa_records": [],
      "tech": ["nginx", "node.js"],
      "words": 125,
      "lines": 8,
      "status_code": 200,
      "content_length": 1024,
      "cdn": false,
      "cdn_name": "",
      "cdn_type": "",
      "knowledgebase": {}
    }
  ]
}
```

### tech_detection Collection

The `tech_detection` collection stores detailed technology detection results in chunks.

#### Schema

```json
{
  "_id": "ObjectId",
  "operation_id": "6071f1234567890abcdef123",  // Reference to operations collection
  "chunk_number": 1,  // Chunk number for pagination
  "created_at": "2023-04-10T12:35:01.234Z",  // Creation timestamp
  "count": 100,  // Number of records in this chunk
  "data": [  // Array of tech detection records
    {
      "url": "https://qa.example.com",
      "host": "qa.example.com",
      "http_status_code": "200",
      "tech_count": 4,
      "technologies": [
        {
          "slug": "amazon-web-services",
          "name": "Amazon Web Services",
          "description": "Amazon Web Services (AWS) is a comprehensive cloud services platform...",
          "confidence": 100,
          "version": null,
          "icon": "Amazon Web Services.svg",
          "website": "https://aws.amazon.com/",
          "cpe": null,
          "categories": [
            {
              "id": 62,
              "slug": "paas",
              "name": "PaaS"
            }
          ]
        }
      ]
    }
  ]
}
```

#### Indexes

The following indexes are created on all collections:

**Operations Collection:**
- `feature`: For querying by feature name
- `status`: For querying by task status
- `parameters.domain`: For querying by domain parameter
- `parameters.keyword`: For querying by keyword parameter




**Pastes Search Collection:**
- `operation_id`: For linking to operations
- `chunk_number`: For pagination
- `domain`: For domain-based queries

**Git Search Collection:**
- `operation_id`: For linking to operations
- `chunk_number`: For pagination
- `repo`: For repository-based queries
- `rule_id`: For filtering by detection rule

**Darkweb Leaks Collection:**
- `operation_id`: For linking to operations
- `chunk_number`: For pagination
- `domain`: For domain-based queries
- `database_name`: For filtering by database name

**Webserver Discovery Collection:**
- `operation_id`: For linking to operations
- `chunk_number`: For pagination
- `domain`: For domain-based queries

**Tech Detection Collection:**
- `operation_id`: For linking to operations
- `chunk_number`: For pagination

## Data Models

### TaskStatus Enum

```python
class TaskStatus(str, Enum):
    PENDING = "pending"    # Task is waiting to be processed
    RUNNING = "running"    # Task is currently being executed
    COMPLETED = "completed"  # Task has completed successfully
    FAILED = "failed"      # Task has failed with an error
    KILLED = "killed"      # Task was terminated
    SUSPENDED = "suspended"  # Task is temporarily suspended
```

### Task Model

```python
class Task(BaseModel):
    id: Optional[str] = None  # Task ID
    feature: str              # Feature name
    parameters: Dict[str, Any]  # Task parameters
    status: TaskStatus = TaskStatus.PENDING  # Task status
    start_time: Optional[datetime] = None  # When task started
    end_time: Optional[datetime] = None  # When task completed
```

### TaskResult Model

```python
class TaskResult(BaseModel):
    job_id: str              # Task ID
    raw_output: List[str] = []  # Raw command output
    structured_output: Optional[Dict[str, Any]] = None  # Processed results
```

### TaskResponse Model

```python
class TaskResponse(BaseModel):
    job_id: str              # Task ID
    status: TaskStatus       # Task status
    feature: str             # Feature name
    start_time: Optional[datetime] = None  # When task started
    end_time: Optional[datetime] = None  # When task completed
    elapsed_seconds: Optional[float] = None  # Task duration
    results: Optional[Dict[str, Any]] = None  # Task results
    error: Optional[str] = None  # Error message
    progress_perc: Optional[int] = None  # Progress percentage
    message: Optional[str] = None  # Additional message
    cache_hit: bool = False  # Whether result is from cache
```




### Request Models

#### TechDetectionPostRequest Model

```python
class TechDetectionPostRequest(BaseModel):
    urls: List[str]  # List of URLs to analyze (max 100)
```

#### WebserverDiscoveryPostRequest Model

```python
class WebserverDiscoveryPostRequest(BaseModel):
    subdomains: List[str]  # List of subdomains to discover webservers for
```

## Task Caching

The API automatically caches task results for a configurable period of time. If you request the same task again within this period, the cached results will be returned instead of starting a new task.

The cache periods are defined in `app/config.py`:

```python
TASK_EXPIRATION = {
    # Default expiration time if not specified
    "default": timedelta(hours=6),
    
    # Feature-specific expiration times
    "subdomain_enumeration": timedelta(hours=24),
    "public_ip_search": timedelta(hours=12),
    "webserver_discovery": timedelta(hours=12),
    "tech_detection": timedelta(days=30),  # Tech detection results are more stable


    "darkweb_leaks": timedelta(hours=6),
    "git_search": timedelta(hours=6),
    "pastes_search": timedelta(hours=6),
    "phishing_domains": timedelta(hours=6),
}
```

## Database Operations

The `app/db/operations.py` module provides a unified interface for working with operations in the database:

### Core Operations

- `init_db()`: Initialize database with indexes
- `create_operation()`: Create a new operation
- `get_operation()`: Get operation by ID
- `update_operation()`: Update operation with new information
- `get_operation_status()`: Get operation status, combining database and Celery information
- `get_celery_task_status()`: Get real-time status from Celery
- `get_running_operation()`: Get running operation by feature and parameters
- `get_recent_operation()`: Get recent completed operation by feature and parameters

### Raw Output Operations

- `add_raw_output()`: Add raw output line to operation

### Chunk-based Data Operations

#### Pastes Search

- `store_pastes_search_chunk()`: Store a chunk of pastes search results
- `get_pastes_search_chunk()`: Get specific chunk of pastes search results

#### Git Search

- `save_git_search_chunk()`: Store a chunk of git search results
- `get_git_search_chunk()`: Get specific chunk of git search results

#### Darkweb Leaks

- `store_darkweb_leaks_chunk()`: Store a chunk of darkweb leaks results
- `get_darkweb_leaks_chunk()`: Get specific chunk of darkweb leaks results

#### Webserver Discovery

- `store_webserver_discovery_chunk()`: Store a chunk of webserver discovery results
- `get_webserver_discovery_chunk()`: Get specific chunk of webserver discovery results
- `get_webserver_urls_by_domain()`: Get all webserver URLs for a specific domain

#### Tech Detection

- `store_tech_detection_chunk()`: Store a chunk of tech detection results
- `get_tech_detection_chunk()`: Get specific chunk of tech detection results
- `get_tech_detection_results_by_url()`: Get all tech detection results for a specific URL

## Chunk-based Data Storage Architecture

For features that can return large amounts of data (pastes search, git search, darkweb leaks, webserver discovery, tech detection), ThreatMesh uses a chunk-based storage approach:

1. **Main Operation**: Basic operation metadata is stored in the `operations` collection
2. **Detailed Data**: Large result sets are split into chunks and stored in separate collections
3. **Pagination**: Chunks allow for efficient pagination when retrieving results
4. **Performance**: This approach prevents single documents from becoming too large
5. **Scalability**: Large datasets can be processed and stored incrementally

### Chunk Structure

Each chunk contains:
- `operation_id`: Links back to the main operation
- `chunk_number`: Sequential number for ordering
- `count`: Number of records in the chunk
- `data`: Array of actual result records
- `created_at`: Timestamp for the chunk

## Raw Output Storage

To optimize database storage and performance:

1. Raw output (such as command output, API responses, etc.) is only stored in the database when a task fails
2. For successful tasks, raw output is processed to extract structured data and then cleared from the database
3. This approach ensures that debug information is available when needed while keeping the database size manageable

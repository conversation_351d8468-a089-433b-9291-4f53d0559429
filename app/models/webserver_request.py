"""
Pydantic models for webserver discovery requests.
"""
from typing import List, Optional
from pydantic import BaseModel, field_validator
import validators


class WebserverDiscoveryRequest(BaseModel):
    """Request model for POST webserver discovery API (domain-based)"""

    domain: str
    filter_status_code: Optional[str] = None

    @field_validator('domain')
    def validate_domain(cls, v):
        """Validate domain format"""
        if not validators.domain(v):
            raise ValueError(f"Invalid domain format: {v}")
        return v


class WebserverDiscoveryPostRequest(BaseModel):
    """Request model for POST webserver discovery API (subdomains in body)"""

    subdomains: List[str]
    filter_status_code: Optional[str] = None

    @field_validator('subdomains')
    def validate_subdomains(cls, v):
        """Validate subdomains list"""
        if not isinstance(v, list):
            raise ValueError("Subdomains must be a list")

        if len(v) == 0:
            raise ValueError("Subdomains list cannot be empty")

        for subdomain in v:
            if not validators.domain(subdomain):
                raise ValueError(f"Invalid subdomain format: {subdomain}")
        return v
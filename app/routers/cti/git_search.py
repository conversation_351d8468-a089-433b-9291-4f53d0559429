"""
Git search routes using git-hound tool.
"""
import logging
import json
from typing import Optional, List, Dict, Any
from datetime import datetime
from math import ceil

from fastapi import APIRouter, HTTPException, status, Query, Depends

from app.db import operations
from app.models.task import TaskStatus, ExecutionResponse
from app.models.git_search_request import GitSearchRequest
from app.tasks.cti.git_search import run_git_search_by_identifiers
from app.utils.response_utils import create_task_response


logger = logging.getLogger(__name__)

router = APIRouter(prefix="/git_search")


@router.post("", response_model=ExecutionResponse)
async def git_search(request: GitSearchRequest) -> ExecutionResponse:
    """
    Start GitHub search for leaks containing the specified identifiers.

    Request Body:
    {
        "identifiers": ["example.com", "<EMAIL>"],
        "git_depth": 10
    }
    """
    try:
        # Extract identifiers and git_depth from request
        identifiers_list = request.identifiers
        git_depth = request.git_depth

        # Start a new task
        logger.info(f"Starting new git search for identifiers {identifiers_list}")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="git_search",
            parameters={"identifiers": identifiers_list, "git_depth": git_depth}
        )

        # Start the task - pass the job_id to the Celery task
        task = run_git_search_by_identifiers.delay(identifiers_list, job_id, chunk_size=100, git_depth=git_depth)
        logger.info(f"Celery task started with task_id {task.id}")

        # Return lightweight execution response
        return ExecutionResponse(
            job_id=job_id,
            status=TaskStatus.PENDING,
            feature="git_search",
            created_at=datetime.now()
        )
    except Exception as e:
        logger.exception(f"Error starting git search task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}",
        )

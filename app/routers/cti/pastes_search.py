"""
Pastes search routes using Google Custom Search Engine.
"""
import logging
from typing import Op<PERSON>, Tuple, List, Dict, Any
from datetime import datetime
from math import ceil

from fastapi import APIRouter, HTTPException, status, Query, Depends

from app.db import operations
from app.models.task import TaskStatus, ExecutionResponse
from app.models.pastes_search_request import PastesSearchRequest
from app.tasks.cti.pastes_search import run_pastes_search_by_domain

from app.utils.response_utils import create_task_response


logger = logging.getLogger(__name__)

router = APIRouter(prefix="/pastes_search")


@router.post("", response_model=ExecutionResponse)
async def pastes_search(request: PastesSearchRequest) -> ExecutionResponse:
    """
    Start pastes search for a domain.

    Request Body:
    {
        "domain": "example.com",
        "secrets": false,
        "cse_page_limit": 10
    }
    """
    try:
        # Extract parameters from request
        domain = request.domain
        secrets = request.secrets
        cse_page_limit = request.cse_page_limit

        # Start a new task
        logger.info(f"Starting new pastes search for domain {domain}")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="pastes_search",
            parameters={"domain": domain, "secrets": secrets, "cse_page_limit": cse_page_limit}
        )

        # Start the task - pass the job_id to the Celery task
        task = run_pastes_search_by_domain.delay(domain, job_id, secrets, chunk_size=100, cse_page_limit=cse_page_limit)
        logger.info(f"Celery task started with task_id {task.id}")

        # Return lightweight execution response
        return ExecutionResponse(
            job_id=job_id,
            status=TaskStatus.PENDING,
            feature="pastes_search",
            created_at=datetime.now()
        )
    except Exception as e:
        logger.exception(f"Error starting pastes search task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}"
        )

"""
Stack vulnerabilities analysis task for ThreatMesh.
"""
import logging
import os
import requests
import csv
import time
import re
from io import StringIO
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple

from app.db import operations
from app.models.task import TaskStatus
from app.tasks.celery_app import celery_app
from app.tasks.executor import handle_task_error, complete_task_success

logger = logging.getLogger(__name__)

# NVD API configuration
NVD_CVE_API_URL = "https://services.nvd.nist.gov/rest/json/cves/2.0"
EXPLOIT_DB_CSV_URL = "https://gitlab.com/exploit-database/exploitdb/-/raw/main/files_exploits.csv"
GITHUB_TRICKEST_BASE_URL = "https://raw.githubusercontent.com/trickest/cve/refs/heads/main"
NUCLEI_TEMPLATES_JSON_URL = "https://raw.githubusercontent.com/projectdiscovery/nuclei-templates/refs/heads/main/cves.json"

# EPSS API configuration
EPSS_API_URL = "https://api.first.org/data/v1/epss"

# Cache for Exploit-DB CSV, Nuclei templates, and EPSS scores
exploit_db_cache = None
nuclei_templates_cache = None
epss_cache = None

@celery_app.task(bind=True)
def run_stack_vulnerabilities(self, domain: str = None, cpe: str = None, job_id: str = None):
    """Run stack vulnerabilities analysis on domain or CPE"""
    try:
        # Clear EPSS cache for fresh data in each task to prevent memory leaks
        # and ensure data freshness across different task executions
        global epss_cache
        epss_cache = None
        
        # Job ID should always be provided by the router
        if not job_id:
            logger.error("No job_id provided to stack vulnerabilities task")
            return {"success": False, "message": "No job_id provided"}

        # Mark as started in database and store Celery task ID
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.RUNNING,
            start_time=datetime.now(),
            task_id=self.request.id  # Store Celery's task ID
        )

        # Update task state in Celery
        self.update_state(state='STARTED', meta={'progress': 0, 'message': 'Starting stack vulnerabilities analysis'})

        # Get CPE list based on input type
        cpe_list = []
        if domain:
            # Get CPEs from tech detection results
            cpe_list, error_msg = _get_cpes_from_domain(domain)
            if not cpe_list:
                if error_msg:
                    operations.update_operation(
                        job_id=job_id,
                        status=TaskStatus.FAILED,
                        error=error_msg,
                        end_time=datetime.now()
                    )
                    return {"job_id": job_id, "success": False, "message": error_msg}
                else:
                    error_msg = "No CPE data found in tech detection results"
                    operations.update_operation(
                        job_id=job_id,
                        status=TaskStatus.FAILED,
                        error=error_msg,
                        end_time=datetime.now()
                    )
                    return {"job_id": job_id, "success": False, "message": error_msg}
        elif cpe:
            # Use the provided CPE directly
            cpe_list = [{
                "cpe": cpe,
                "host": "N/A",
                "tech_name": "N/A",
                "tech_version": "N/A"
            }]
        else:
            error_msg = "Either domain or cpe must be provided"
            operations.update_operation(
                job_id=job_id,
                status=TaskStatus.FAILED,
                error=error_msg,
                end_time=datetime.now()
            )
            return {"job_id": job_id, "success": False, "message": error_msg}

        # Update progress
        self.update_state(state='STARTED', meta={'progress': 10, 'message': f'Found {len(cpe_list)} CPE entries to analyze'})

        # Collect all CVE data and IDs first for bulk EPSS fetching
        self.update_state(state='STARTED', meta={'progress': 15, 'message': 'Collecting CVE data for analysis'})
        all_cve_ids = set()
        cpe_cve_cache = {}  # Cache CVE data to avoid duplicate NVD API calls
        
        # First pass: collect all CVE data from all CPEs
        for cpe_info in cpe_list:
            try:
                cve_data = _fetch_cve_details_by_cpe(cpe_info["cpe"])
                cpe_cve_cache[cpe_info["cpe"]] = cve_data
                if cve_data:
                    for cve in cve_data:
                        all_cve_ids.add(cve["cve_id"])
            except Exception as e:
                logger.warning(f"Error collecting CVE data for CPE {cpe_info['cpe']}: {e}")
                # Don't cache the error - let it retry in the second pass
                continue

        # Bulk fetch EPSS scores for all CVEs
        if all_cve_ids:
            cve_count = len(all_cve_ids)
            if cve_count > 5000:
                logger.warning(f"Large number of CVEs detected ({cve_count}). EPSS fetching may take longer.")
            
            self.update_state(state='STARTED', meta={'progress': 20, 'message': f'Fetching EPSS scores for {cve_count} CVEs'})
            _fetch_epss_scores_bulk(list(all_cve_ids))
        else:
            logger.info("No CVEs found for EPSS analysis")

        # Analyze each CPE for vulnerabilities
        stack_vulnerabilities = []
        total_cves = 0
        successful_analyses = 0
        failed_analyses = 0

        for i, cpe_info in enumerate(cpe_list):
            try:
                # Update progress for each CPE
                progress = 25 + (60 * (i + 1) / len(cpe_list))
                self.update_state(state='STARTED', meta={
                    'progress': int(progress), 
                    'message': f'Analyzing CPE {i+1}/{len(cpe_list)}: {cpe_info["cpe"]}'
                })
                
                # Get CVE details for this CPE from cache, or retry if not cached
                cve_data = cpe_cve_cache.get(cpe_info["cpe"])
                if cve_data is None and cpe_info["cpe"] not in cpe_cve_cache:
                    # CPE failed in first pass, retry now
                    logger.info(f"Retrying CVE fetch for CPE {cpe_info['cpe']}")
                    cve_data = _fetch_cve_details_by_cpe(cpe_info["cpe"])
                
                if cve_data:
                    # Get exploit information and EPSS scores for each CVE
                    for cve in cve_data:
                        # Get exploit links (optimized to fetch Trickest content only once)
                        exploit_db_links, potential_github_pocs, nuclei_templates = _get_all_exploit_links(cve["cve_id"])
                        
                        cve["public_exploits"] = {
                            "exploitDB": exploit_db_links,
                            "nuclei_templates": nuclei_templates,
                            "potential_github_pocs": potential_github_pocs
                        }
                        
                        # Add EPSS score from cache
                        epss_data = _get_epss_score_from_cache(cve["cve_id"])
                        cve["epss_score"] = epss_data
                    
                    # Create vulnerability entry
                    vulnerability = {
                        "cpe": cpe_info["cpe"],
                        "host": cpe_info["host"],
                        "tech_name": cpe_info["tech_name"],
                        "tech_version": cpe_info["tech_version"],
                        "vulnerability_count": len(cve_data),
                        "vulnerabilities": cve_data
                    }
                    
                    stack_vulnerabilities.append(vulnerability)
                    total_cves += len(cve_data)
                    successful_analyses += 1
                else:
                    # No vulnerabilities found for this CPE
                    vulnerability = {
                        "cpe": cpe_info["cpe"],
                        "host": cpe_info["host"],
                        "tech_name": cpe_info["tech_name"],
                        "tech_version": cpe_info["tech_version"],   
                        "vulnerabilities": [],
                        "vulnerability_count": 0,
                        "message": "No vulnerabilities found for this CPE"
                    }
                    
                    stack_vulnerabilities.append(vulnerability)
                    successful_analyses += 1
                    
            except Exception as e:
                logger.warning(f"Stack vulnerability analysis failed for CPE {cpe_info['cpe']}: {e}")
                
                # Include failed CPE in results
                vulnerability = {
                    "cpe": cpe_info["cpe"],
                    "host": cpe_info["host"],
                    "tech_name": cpe_info["tech_name"],
                    "tech_version": cpe_info["tech_version"],
                    "vulnerabilities": [],
                    "vulnerability_count": 0,
                    "error": str(e)
                }
                
                stack_vulnerabilities.append(vulnerability)
                failed_analyses += 1
                continue

        # Update progress
        self.update_state(state='STARTED', meta={'progress': 90, 'message': 'Finalizing results'})

        # Calculate statistics
        high_severity_count = 0
        medium_severity_count = 0
        low_severity_count = 0
        critical_severity_count = 0
        epss_high_risk_count = 0  # EPSS > 0.5 (high probability of exploitation)
        epss_available_count = 0
        epss_scores = []

        for vuln in stack_vulnerabilities:
            for cve in vuln.get("vulnerabilities", []):
                severity = cve.get("severity", "").upper()
                if severity == "CRITICAL":
                    critical_severity_count += 1
                elif severity == "HIGH":
                    high_severity_count += 1
                elif severity == "MEDIUM":
                    medium_severity_count += 1
                elif severity == "LOW":
                    low_severity_count += 1
                
                # Count EPSS statistics
                epss_data = cve.get("epss_score", {})
                epss_score = epss_data.get("epss")
                if epss_score is not None:
                    epss_available_count += 1
                    epss_scores.append(epss_score)
                    if epss_score > 0.5:
                        epss_high_risk_count += 1
                
        # Calculate EPSS statistics
        epss_stats = {
            "total_with_epss": epss_available_count,
            "total_without_epss": total_cves - epss_available_count,
            "high_risk_count": epss_high_risk_count,  # EPSS > 0.5
            "coverage_percentage": round((epss_available_count / total_cves * 100), 2) if total_cves > 0 else 0,
            "average_epss": round(sum(epss_scores) / len(epss_scores), 4) if epss_scores else None,
            "max_epss": round(max(epss_scores), 4) if epss_scores else None,
            "min_epss": round(min(epss_scores), 4) if epss_scores else None
        }

        # Store stack vulnerabilities results in chunks for pagination
        from app.db.operations import store_stack_vulnerabilities_chunk
        
        chunk_size = 50
        chunk_number = 0
        
        for i in range(0, len(stack_vulnerabilities), chunk_size):
            chunk_data = stack_vulnerabilities[i:i + chunk_size]
            store_stack_vulnerabilities_chunk(job_id, chunk_number, chunk_data)
            chunk_number += 1

        # Prepare structured output with stats (detailed data is in chunks)
        structured_output = {
            "stats": {
                "total": total_cves,
                "total_chunks": chunk_number,
                "chunk_size": chunk_size,
                "total_cpes_analyzed": len(cpe_list),
                "successful_analyses": successful_analyses,
                "failed_analyses": failed_analyses,
                "severity_breakdown": {
                    "critical": critical_severity_count,
                    "high": high_severity_count,
                    "medium": medium_severity_count,
                    "low": low_severity_count
                },
                "epss_statistics": epss_stats
            }
        }
        
        logger.info(f"Stored {len(stack_vulnerabilities)} stack vulnerabilities in {chunk_number} chunks for operation {job_id}")

        # Update database with final results
        result = complete_task_success(
            job_id=job_id,
            structured_output=structured_output,
            message=f"Stack vulnerabilities analysis completed. Found {total_cves} vulnerabilities across {len(cpe_list)} CPEs. EPSS scores retrieved for {epss_available_count} CVEs."
        )
        # Add additional fields specific to this task
        result["total_vulnerabilities"] = total_cves
        result["epss_coverage"] = epss_available_count
        return result

    except Exception as e:
        return handle_task_error(job_id, e, "stack vulnerabilities analysis")


def _get_cpes_from_domain(domain: str) -> Tuple[List[Dict[str, str]], Optional[str]]:
    """
    Get CPE information from the most recent successful tech detection for a domain
    
    Args:
        domain: Domain to get CPE data for
        
    Returns:
        Tuple[List[Dict], Optional[str]]: (List of CPE information, Error message if any)
    """
    try:
        # Get all tech detection results for the domain using the new chunk-based storage
        from app.db.operations import get_all_tech_detection_results_by_domain
        
        tech_detections = get_all_tech_detection_results_by_domain(domain)
        
        if not tech_detections:
            return [], f"No tech detection results found for domain {domain}. Please run tech detection first."
        
        cpe_list = []
        technologies_found = False
        technologies_with_version = False
        
        for detection in tech_detections:
            host = detection.get("host", "")
            technologies = detection.get("technologies", [])
            
            if technologies:
                technologies_found = True
            
            for tech in technologies:
                cpe = tech.get("cpe")
                version = tech.get("version")
                
                if version and version != "N/A":
                    technologies_with_version = True
                
                # Skip if any field is null/None/empty or invalid
                # Only process if BOTH version and CPE are present and valid
                if not version or not cpe or cpe == "N/A":
                    continue
                
                # Process CPE - at this point we're guaranteed to have both version and valid CPE
                # If CPE ends with generic wildcards, update CPE with actual version
                #cpe:2.3:a:php:php:*:*:*:*:*:*:*:* + version 7.4.33 → cpe:2.3:a:php:php:7.4.33:*:*:*:*:*:*:*
                cpe_parts = cpe.split(":")
                if len(cpe_parts) >= 6 and cpe_parts[5] == "*":
                    cpe_parts[5] = version
                    final_cpe = ":".join(cpe_parts)
                else:
                    final_cpe = cpe
                
                cpe_info = {
                    "cpe": final_cpe,
                    "host": host,
                    "tech_name": tech.get("name", "N/A"),
                    "tech_version": version
                }
                cpe_list.append(cpe_info)
        
        if not cpe_list:
            if not technologies_found:
                return [], f"No technologies detected for domain {domain}. The target may not be responding or may not use detectable technologies."
            elif not technologies_with_version:
                return [], f"No technologies with version information found for domain {domain}. CPE matching requires version data."
            else:
                return [], f"No CPE data found in tech detection results for domain {domain}. The detected technologies may not have CPE mappings."
        
        return cpe_list, None
        
    except Exception as e:
        logger.error(f"Error getting CPEs from domain {domain}: {e}")
        return [], f"Error retrieving tech detection data for domain {domain}: {str(e)}"


def _make_api_request(url: str, params: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
    """Make API request with retry logic and rate limiting"""
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36"
    }
    
    # Add API key if available
    nvd_api_key = os.getenv("NVD_CVE_API_KEY", "")
    if nvd_api_key:
        headers["apiKey"] = nvd_api_key
    
    for attempt in range(3):
        try:
            response = requests.get(url, params=params, headers=headers, timeout=60)
            
            if response.status_code == 429:
                retry_after = int(response.headers.get('Retry-After', 6))
                logger.warning(f"Rate limit exceeded. Retrying in {retry_after}s...")
                time.sleep(retry_after+1)
                continue
                
            response.raise_for_status()
            return response.json()
            
        except requests.RequestException as e:
            logger.warning(f"Request failed (attempt {attempt + 1}): {e}")
            time.sleep(2 ** attempt)
    
    return None


def _fetch_cve_details_by_cpe(cpe_string: str) -> List[Dict[str, Any]]:
    """Fetch CVE details for a given CPE string from NVD"""
    try:
        params = {"cpeName": cpe_string}
        data = _make_api_request(NVD_CVE_API_URL, params)
        
        if not data:
            return []
        
        vulnerabilities = []
        for item in data.get('vulnerabilities', []):
            cve = item['cve']
            cve_id = cve['id']
            
            # Extract severity from metrics using primary CVSS and latest version available
            severity = "UNKNOWN"
            cve_metrics = cve.get('metrics', {})
            
            # Helper function to find primary metric
            def find_primary_metric(metrics_list):
                for metric in metrics_list:
                    if metric.get('type') == 'Primary':
                        return metric
                return metrics_list[0] if metrics_list else None
            
            
            # Prioritize latest CVSS version for severity: V4.0 > V3.1 > V3.0 > V2.0
            # Use primary metric when available
            if 'cvssMetricV40' in cve_metrics and cve_metrics['cvssMetricV40']:
                severity_metric = find_primary_metric(cve_metrics['cvssMetricV40'])
                if severity_metric:
                    severity = severity_metric.get('cvssData', {}).get('baseSeverity', "UNKNOWN")
            elif 'cvssMetricV31' in cve_metrics and cve_metrics['cvssMetricV31']:
                severity_metric = find_primary_metric(cve_metrics['cvssMetricV31'])
                if severity_metric:
                    severity = severity_metric.get('cvssData', {}).get('baseSeverity', "UNKNOWN")
            elif 'cvssMetricV30' in cve_metrics and cve_metrics['cvssMetricV30']:
                severity_metric = find_primary_metric(cve_metrics['cvssMetricV30'])
                if severity_metric:
                    severity = severity_metric.get('cvssData', {}).get('baseSeverity', "UNKNOWN")
            elif 'cvssMetricV2' in cve_metrics and cve_metrics['cvssMetricV2']:
                severity_metric = find_primary_metric(cve_metrics['cvssMetricV2'])
                if severity_metric:
                    severity = severity_metric.get('baseSeverity', "UNKNOWN")
            
            # Extract weaknesses - prioritize primary but use secondary if primary has no proper CWE IDs
            weaknesses = []
            primary_weaknesses = []
            secondary_weaknesses = []
            
            # Separate primary and secondary weaknesses
            for weakness in cve.get('weaknesses', []):
                weakness_type = weakness.get('type', '')
                for desc in weakness.get('description', []):
                    if desc.get('lang') == 'en':
                        weakness_value = desc.get('value', '')
                        if weakness_type == 'Primary':
                            primary_weaknesses.append(weakness_value)
                        elif weakness_type == 'Secondary':
                            secondary_weaknesses.append(weakness_value)
            
            # Helper function to check if weakness is a proper CWE ID
            def is_proper_cwe(weakness_val):
                return weakness_val.startswith('CWE-') and not weakness_val.startswith('NVD-CWE')
            
            # Use primary weaknesses if they contain proper CWE IDs, otherwise use secondary
            proper_primary = [w for w in primary_weaknesses if is_proper_cwe(w)]
            if proper_primary:
                weaknesses = proper_primary
            else:
                proper_secondary = [w for w in secondary_weaknesses if is_proper_cwe(w)]
                weaknesses = proper_secondary if proper_secondary else primary_weaknesses
            
            # Extract English description
            description = 'N/A'
            for desc in cve.get('descriptions', []):
                if desc.get('lang') == 'en':
                    description = desc.get('value', 'N/A')
                    break
            
            # Check if CVE is in CISA Known Exploited Vulnerabilities (KEV) catalog
            kev_status = bool(cve.get('cisaExploitAdd'))
            
            vulnerability = {
                "cve_id": cve_id,
                "description": description,
                "severity": severity,
                "weakness(CWE-ID)": ', '.join(weaknesses) if weaknesses else 'N/A',
                "published": cve.get('published', 'N/A'),
                "lastModified": cve.get('lastModified', 'N/A'),
                "KEV": kev_status,
                "metrics": cve_metrics, 
                "nvd_link": f"https://nvd.nist.gov/vuln/detail/{cve_id}"
            }
            
            vulnerabilities.append(vulnerability)
        
        return vulnerabilities
        
    except Exception as e:
        logger.error(f"Error fetching CVE details for CPE {cpe_string}: {e}")
        return []


def _get_exploit_db():
    """Get Exploit-DB data with caching"""
    global exploit_db_cache
    if exploit_db_cache is not None:
        return exploit_db_cache
    
    try:
        response = requests.get(EXPLOIT_DB_CSV_URL, timeout=30)
        response.raise_for_status()
        csv_content = StringIO(response.text)
        reader = csv.DictReader(csv_content)
        exploit_db_cache = {row['id']: row for row in reader}
        return exploit_db_cache
    except Exception as e:
        logger.error(f"Failed to fetch Exploit-DB: {e}")
        return {}


def _get_nuclei_templates():
    """Get Nuclei templates data with caching"""
    import json
    global nuclei_templates_cache
    if nuclei_templates_cache is not None:
        return nuclei_templates_cache
    
    try:
        response = requests.get(NUCLEI_TEMPLATES_JSON_URL, timeout=30)
        response.raise_for_status()
        nuclei_templates_cache = {}
        
        # Parse JSONL format (one JSON object per line)
        for line in response.text.strip().split('\n'):
            if line.strip():
                try:
                    template_data = json.loads(line.strip())
                    cve_id = template_data.get('ID', '')
                    if cve_id:
                        # Store CVE IDs in lowercase for consistent case-insensitive matching
                        nuclei_templates_cache[cve_id.lower()] = template_data
                except json.JSONDecodeError as e:
                    logger.debug(f"Failed to parse nuclei template line: {e}")
                    continue
        
        return nuclei_templates_cache
    except Exception as e:
        logger.error(f"Failed to fetch Nuclei templates: {e}")
        return {}


def _fetch_epss_scores_bulk(cve_ids: List[str]) -> None:
    """
    Fetch EPSS scores for multiple CVEs using bulk API calls.
    Results are cached globally for the current task execution.
    
    Args:
        cve_ids: List of CVE IDs to fetch EPSS scores for
    """
    global epss_cache
    if epss_cache is None:
        epss_cache = {}
    
    # Filter out CVEs that are already cached
    uncached_cves = [cve_id for cve_id in cve_ids if cve_id not in epss_cache]
    
    if not uncached_cves:
        logger.info("All EPSS scores already cached")
        return
    
    logger.info(f"Fetching EPSS scores for {len(uncached_cves)} CVEs")
    
    # Split CVE IDs into batches - limit to 50 CVEs per batch for reliability
    # Also respect the 2000 character URL limit as secondary constraint
    batches = []
    current_batch = []
    current_length = 0
    max_cves_per_batch = 50
    
    for cve_id in uncached_cves:
        # Account for comma separator (except for first item)
        item_length = len(cve_id) + (1 if current_batch else 0)
        
        # Check both CVE count limit and character limit
        if (len(current_batch) >= max_cves_per_batch or 
            current_length + item_length > 1900):  # Leave buffer for URL encoding
            
            if current_batch:
                batches.append(current_batch)
                current_batch = [cve_id]
                current_length = len(cve_id)
            else:
                # Single CVE is too long, add it anyway
                batches.append([cve_id])
        else:
            current_batch.append(cve_id)
            current_length += item_length
    
    if current_batch:
        batches.append(current_batch)
    
    logger.info(f"Created {len(batches)} EPSS API batches (max 50 CVEs per batch)")
    
    # Log batch sizes for debugging
    batch_sizes = [len(batch) for batch in batches]
    logger.debug(f"Batch sizes: {batch_sizes}")
    
    # Fetch EPSS scores for each batch
    for i, batch in enumerate(batches):
        try:
            logger.debug(f"Fetching EPSS batch {i+1}/{len(batches)} with {len(batch)} CVEs")
            logger.debug(f"Batch {i+1} CVEs: {batch[:5]}{'...' if len(batch) > 5 else ''}")  # Log first 5 CVEs
            
            # Prepare API request
            cve_list = ','.join(batch)
            params = {
                'cve': cve_list,
                'pretty': 'true'
            }
            
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36"
            }
            
            response = requests.get(EPSS_API_URL, params=params, headers=headers, timeout=60)
            
            # Handle rate limiting (429 Too Many Requests)
            if response.status_code == 429:
                retry_after = int(response.headers.get('Retry-After', 60))  # Default to 60s if not specified
                logger.warning(f"EPSS API rate limit exceeded for batch {i+1}. Retrying in {retry_after}s...")
                time.sleep(retry_after + 1)  # Add 1 second buffer
                
                # Retry the request once
                response = requests.get(EPSS_API_URL, params=params, headers=headers, timeout=60)
            
            response.raise_for_status()
            data = response.json()
            
            if data.get('status') == 'OK':
                # Process EPSS data and cache it
                epss_data_items = data.get('data', [])
                if not epss_data_items:
                    logger.warning(f"EPSS API returned OK status but no data for batch {i+1}")
                
                # Track which CVEs were returned vs requested
                returned_cves = set()
                
                for item in epss_data_items:
                    cve_id = item.get('cve', '')
                    if not cve_id:
                        continue  # Skip items without CVE ID
                    
                    try:
                        epss_score = float(item.get('epss', 0))
                        percentile = float(item.get('percentile', 0))
                        date = item.get('date', '')
                        
                        epss_cache[cve_id] = {
                            'epss': epss_score,
                            'percentile': percentile,
                            'date': date
                        }
                        returned_cves.add(cve_id)
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Invalid EPSS data for CVE {cve_id}: {e}")
                        epss_cache[cve_id] = {
                            'epss': None,
                            'percentile': None,
                            'date': None,
                            'error': f"Invalid data format: {e}"
                        }
                        returned_cves.add(cve_id)
                
                # Check for missing CVEs that weren't returned by the API
                requested_cves = set(batch)
                missing_cves = requested_cves - returned_cves
                
                if missing_cves:
                    logger.warning(f"Batch {i+1}: {len(missing_cves)} CVEs not returned by EPSS API: {list(missing_cves)[:5]}{'...' if len(missing_cves) > 5 else ''}")
                    
                    # Cache empty results for missing CVEs
                    for missing_cve in missing_cves:
                        epss_cache[missing_cve] = {
                            'epss': None,
                            'percentile': None,
                            'date': None,
                            'error': 'CVE not found in EPSS database'
                        }
                
                logger.debug(f"Successfully cached EPSS scores for batch {i+1}: {len(returned_cves)}/{len(batch)} CVEs found")
            else:
                logger.warning(f"EPSS API returned status: {data.get('status')} for batch {i+1}")
                
                # Cache empty results for CVEs in this batch to avoid re-fetching
                for cve_id in batch:
                    epss_cache[cve_id] = {
                        'epss': None,
                        'percentile': None,
                        'date': None,
                        'error': f"API error: {data.get('status')}"
                    }
            
            # Add delay between batches to be respectful to the API
            if i < len(batches) - 1:
                # Increase delay slightly for larger datasets
                delay = 1.5 if len(batches) > 10 else 1.0
                time.sleep(delay)  # Should be safe for 1000 requests/minute limit
                
        except Exception as e:
            logger.error(f"Error fetching EPSS scores for batch {i+1}: {e}")
            
            # Cache empty results for CVEs in this batch to avoid re-fetching
            for cve_id in batch:
                epss_cache[cve_id] = {
                    'epss': None,
                    'percentile': None,
                    'date': None,
                    'error': str(e)
                }


def _get_epss_score_from_cache(cve_id: str) -> Dict[str, Any]:
    """
    Get EPSS score for a CVE from cache.
    
    Args:
        cve_id: CVE ID to get EPSS score for
        
    Returns:
        Dict containing EPSS data or None if not available
    """
    global epss_cache
    
    if epss_cache is None:
        return {
            'epss': None,
            'percentile': None,
            'date': None,
            'error': 'EPSS cache not initialized'
        }
    
    return epss_cache.get(cve_id, {
        'epss': None,
        'percentile': None,
        'date': None,
        'error': 'CVE not found in EPSS data'
    })


def _get_all_exploit_links(cve_id: str) -> Tuple[List[str], List[str], List[str]]:
    """
    Get ExploitDB, potential GitHub POC links, and Nuclei templates for a CVE in a single optimized call.
    This fetches Trickest content only once and extracts both types of links.
    
    Returns:
        tuple: (exploitdb_links, potential_github_pocs, nuclei_templates)
    """
    # First search ExploitDB CSV
    exploitdb_links = []
    try:
        cve_id_lower = cve_id.lower()
        exploit_db = _get_exploit_db()
        
        for exp_id, data in exploit_db.items():
            if cve_id_lower in data.get('codes', '').lower():
                exploit_link = f"https://www.exploit-db.com/exploits/{exp_id}"
                exploitdb_links.append(exploit_link)
    except Exception as e:
        logger.error(f"Error searching ExploitDB CSV for {cve_id}: {e}")
    
    # Fetch Trickest content once and extract both types of links
    potential_github_pocs = []
    trickest_exploitdb_links = []
    
    try:
        year = cve_id.split('-')[1]
        url = f"{GITHUB_TRICKEST_BASE_URL}/{year}/{cve_id}.md"
        
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # Extract ExploitDB links from Trickest content (as fallback)
            trickest_exploitdb_links = list(set(re.findall(r'https://(?:www\.)?exploit-db\.com/exploits/\d+', content)))
            
            # Extract GitHub POC links from GitHub section
            github_section_match = re.search(r'(?i)^#+\s*github.*?\n(.*?)(?=^#+|\Z)', content, re.MULTILINE | re.DOTALL)
            
            if github_section_match:
                github_content = github_section_match.group(1)
                all_github_links = list(set(re.findall(r'https://github\.com/[^\s)]+', github_content)))
                
                if all_github_links:
                    # Prioritize links that contain the CVE ID
                    cve_id_lower = cve_id.lower()
                    cve_specific_links = [link for link in all_github_links if cve_id_lower in link.lower()]
                    
                    if cve_specific_links:
                        potential_github_pocs = cve_specific_links[:5]
                    else:
                        potential_github_pocs = all_github_links[:5]
    
    except Exception as e:
        logger.debug(f"No Trickest info found for {cve_id}: {e}")
    
    # If no ExploitDB links found in CSV, use Trickest fallback
    if not exploitdb_links and trickest_exploitdb_links:
        exploitdb_links = trickest_exploitdb_links
    
    # Search for Nuclei templates
    nuclei_templates = []
    try:
        nuclei_data = _get_nuclei_templates()
        # CVE IDs are stored in lowercase in our cache, so convert for matching
        cve_id_lower = cve_id.lower()
        if cve_id_lower in nuclei_data:
            template_info = nuclei_data[cve_id_lower]
            file_path = template_info.get('file_path', '')
            if file_path:
                template_link = f"https://github.com/projectdiscovery/nuclei-templates/blob/main/{file_path}"
                nuclei_templates.append(template_link)
    except Exception as e:
        logger.debug(f"Error searching Nuclei templates for {cve_id}: {e}")
    
    return exploitdb_links[:5], potential_github_pocs, nuclei_templates  # Limit ExploitDB and GitHub to 5 results 
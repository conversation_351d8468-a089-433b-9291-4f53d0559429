"""
Subdomain enumeration task.
"""
import logging
import json
import uuid
from datetime import datetime
from collections import defaultdict

from app.db import operations
from app.models.task import TaskStatus
from app.tasks.celery_app import celery_app
from app.tasks.executor import execute_command, handle_task_error, complete_task_success, handle_command_error, parse_command_output

logger = logging.getLogger(__name__)

@celery_app.task(bind=True)
def run_subdomain_enumeration(self, domain: str, job_id: str = None, quick: bool = False):
    """Run subdomain enumeration for a domain

    Args:
        domain: Target domain to enumerate subdomains for
        job_id: Optional job ID for tracking
        quick: If True, uses faster enumeration with limited sources
    """
    try:
        # Create operation if needed
        if not job_id:
            job_id = operations.create_operation(
                feature="subdomain_enumeration",
                parameters={"domain": domain, "quick": quick}
            )

        # Mark as started in database and store Celery task ID
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.RUNNING,
            start_time=datetime.now(),
            task_id=self.request.id  # Store Celery's task ID
        )

        # Update task state in Celery (not in database)
        self.update_state(state='STARTED', meta={'progress': 0, 'message': 'Started subdomain enumeration'})

        # Execute command based on quick mode
        if quick:
            # Quick mode: faster enumeration with limited sources
            subfinder_cmd = [
                "/opt/subfinder",
                "-d", domain,
                "-silent",
                "-json",
                "-nW",
                "-rL", "/opt/wordlists/resolvers-trusted.txt",
                "-duc",
                "-exclude-ip", "127.0.0.1",
                "-max-time", "20",
            ]
            logger.info(f"Running quick subdomain enumeration for {domain}")
        else:
            # Full mode: comprehensive enumeration with all sources
            subfinder_cmd = [
                "/opt/subfinder",
                "-d", domain,
                "-silent",
                "-json",
                "-nW",
                "-rL", "/opt/wordlists/resolvers-trusted.txt",
                "-all",
                "-duc",
                "-provider-config", "/opt/configs/subfinder-provider.yaml",
                "-exclude-ip", "127.0.0.1",
                "-max-time", "20",
            ]
            logger.info(f"Running full subdomain enumeration for {domain}")
        success, output = execute_command(job_id, subfinder_cmd)
        
        # Update progress in Celery (not in database)
        self.update_state(state='STARTED', meta={'progress': 50, 'message': 'Processing results'})

        # Process results if successful
        if success:
            # Parse JSON output from subfinder
            subdomains = set()
            sources_dict = defaultdict(set)

            # Split output into lines for processing
            raw_output = parse_command_output(output)

            for line in raw_output:
                try:
                    data = json.loads(line)
                    host, source = data.get("host"), data.get("source")

                    if host:
                        subdomains.add(host)
                        if source:
                            sources_dict[source].add(host)
                except json.JSONDecodeError as e:
                    error_msg = f"Failed to parse JSON: {line}, error: {e}"
                    logger.warning(error_msg)

            # Update progress in Celery (not in database)
            self.update_state(state='STARTED', meta={'progress': 90, 'message': 'Finalizing results'})

            # Prepare and store results in chunks
            sorted_subdomains = sorted(subdomains)
            source_counts = {source: len(domains) for source, domains in sources_dict.items()}

            # Store subdomains in chunks of 500
            chunk_size = 500
            total_subdomains = len(sorted_subdomains)
            chunk_number = 0

            for i in range(0, total_subdomains, chunk_size):
                chunk_data = sorted_subdomains[i:i + chunk_size]
                operations.store_subdomain_enumeration_chunk(job_id, chunk_number, chunk_data)
                chunk_number += 1

            # Prepare structured output with stats (detailed data is in chunks)
            structured_output = {
                "stats": {
                    "total": total_subdomains,
                    "total_chunks": chunk_number,
                    "chunk_size": chunk_size,
                    "sources": source_counts,
                    "scan_parameters": {
                        "domain": domain,
                        "quick": quick
                    }
                }
            }

            # Update database with final results
            return complete_task_success(
                job_id=job_id,
                structured_output=structured_output,
                message=f"Found {len(sorted_subdomains)} subdomains",
                count=len(sorted_subdomains)
            )
        else:
            # If command execution failed, update database with error
            return handle_command_error(job_id, output)

    except Exception as e:
        return handle_task_error(job_id, e, f"subdomain enumeration for domain {domain}")

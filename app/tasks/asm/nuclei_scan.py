"""
Nuclei vulnerability scanning task.
"""
import logging
import json
import tempfile
import os
import subprocess
import uuid
from datetime import datetime
from typing import List, Dict, Any

from app.db import operations
from app.models.task import TaskStatus
from app.tasks.celery_app import celery_app
from app.tasks.executor import execute_command

logger = logging.getLogger(__name__)

@celery_app.task(bind=True)
def run_nuclei_scan(self, cve_ids: List[str], targets: List[str], distributed: bool = False, job_id: str = None):
    """Run nuclei vulnerability scan on targets with specified CVE IDs"""
    try:
        # Job ID should always be provided by the router
        if not job_id:
            logger.error("No job_id provided to nuclei scan task")
            return {"success": False, "message": "No job_id provided"}

        # Mark as started in database and store Celery task ID
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.RUNNING,
            start_time=datetime.now(),
            task_id=self.request.id  # Store Celery's task ID
        )

        # Update task state in Celery
        self.update_state(state='STARTED', meta={'progress': 0, 'message': 'Starting nuclei vulnerability scan'})

        # Validate inputs
        if not cve_ids:
            error_msg = "No CVE IDs provided for nuclei scan"
            operations.update_operation(
                job_id=job_id,
                status=TaskStatus.FAILED,
                error=error_msg,
                end_time=datetime.now()
            )
            return {"job_id": job_id, "success": False, "message": error_msg}

        if not targets:
            error_msg = "No targets provided for nuclei scan"
            operations.update_operation(
                job_id=job_id,
                status=TaskStatus.FAILED,
                error=error_msg,
                end_time=datetime.now()
            )
            return {"job_id": job_id, "success": False, "message": error_msg}

        # Update progress
        self.update_state(state='STARTED', meta={'progress': 10, 'message': f'Scanning {len(targets)} targets for {len(cve_ids)} CVE IDs'})

        # Choose execution mode
        if distributed:
            # Distributed execution using axiom-shell
            vulnerabilities = _run_distributed_scan(self, cve_ids, targets, job_id)
        else:
            # Single execution using local nuclei
            vulnerabilities = _run_single_scan(self, cve_ids, targets, job_id)

        # Update progress
        self.update_state(state='STARTED', meta={'progress': 90, 'message': 'Finalizing results'})

        # Calculate statistics
        total_vulnerabilities = len(vulnerabilities)
        unique_templates = len(set(vuln.get("template-id", "") for vuln in vulnerabilities))
        unique_hosts = len(set(vuln.get("host", "") for vuln in vulnerabilities))
        
        # Prepare structured output
        structured_output = {
            "data": vulnerabilities,
            "stats": {
                "total": total_vulnerabilities,
                "unique_hosts": unique_hosts,
                "scan_mode": "distributed" if distributed else "single",
                "targets_scanned": len(targets),
                "cve_ids_tested": len(cve_ids)
            }
        }

        # Update database with final results
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.COMPLETED,
            structured_output=structured_output,
            raw_output=[],  # Clear raw output on success
            end_time=datetime.now(),
            progress_perc=100
        )

        return {
            "job_id": job_id,
            "success": True,
            "message": f"Nuclei scan completed. Found {total_vulnerabilities} vulnerabilities across {len(targets)} targets",
            "count": total_vulnerabilities
        }

    except Exception as e:
        logger.exception(f"Error in nuclei scan: {e}")

        # In case of failure, update database with error
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.FAILED,
            error=str(e),
            end_time=datetime.now()
        )

        return {"job_id": job_id, "success": False, "message": str(e)}


def _run_single_scan(task_instance, cve_ids: List[str], targets: List[str], job_id: str) -> List[Dict[str, Any]]:
    """Run nuclei scan using local execution"""
    
    # Create temporary file with targets
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        for target in targets:
            f.write(f"{target}\n")
        targets_file = f.name

    # Create temporary output file
    output_file = f"/tmp/nuclei-scan-{uuid.uuid4().hex}.jsonl"

    try:
        # Update progress
        task_instance.update_state(state='STARTED', meta={'progress': 20, 'message': 'Running nuclei scan'})

        # Prepare CVE IDs string
        cve_ids_str = ",".join(cve_ids)

        # Execute nuclei command
        nuclei_cmd = [
            "/opt/nuclei",
            "-l", targets_file,
            "-jsonl",
            "-t", "/root/nuclei-templates/",
            "-id", cve_ids_str,
            "-o", output_file
        ]
        print(f"Nuclei command: {nuclei_cmd}")
        
        success, output = execute_command(job_id, nuclei_cmd)

        # Update progress
        task_instance.update_state(state='STARTED', meta={'progress': 70, 'message': 'Processing scan results'})

        # Process results if successful
        if success:
            return _parse_nuclei_results(output_file)
        else:
            raise Exception(f"Nuclei scan failed: {output}")

    finally:
        # Clean up temporary files
        try:
            os.unlink(targets_file)
            if os.path.exists(output_file):
                os.unlink(output_file)
        except Exception as e:
            logger.warning(f"Failed to clean up temporary files: {e}")


def _run_distributed_scan(task_instance, cve_ids: List[str], targets: List[str], job_id: str) -> List[Dict[str, Any]]:
    """Run nuclei scan using distributed execution with axiom-shell"""
    
    # Create temporary directory and files
    temp_dir = f"nuclei-scan-{uuid.uuid4().hex}"
    targets_file = f"targets-{uuid.uuid4().hex}.txt"
    results_file = f"nuclei-scan-results-{uuid.uuid4().hex}.jsonl"
    
    # Create targets file locally
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        for target in targets:
            f.write(f"{target}\n")
        local_targets_file = f.name

    try:
        # Update progress
        task_instance.update_state(state='STARTED', meta={'progress': 15, 'message': 'Preparing distributed scan'})

        # Check if axiom-shell container is available
        try:
            result = subprocess.run(
                ["docker", "ps", "--filter", "name=axiom-shell", "--format", "{{.Names}}"],
                capture_output=True,
                text=True,
                timeout=10
            )
            if "axiom-shell" not in result.stdout:
                raise Exception("axiom-shell container not found. Please ensure axiom-shell container is running for distributed scans.")
        except subprocess.TimeoutExpired:
            raise Exception("Timeout checking for axiom-shell container")

        # Copy targets file to axiom-shell container
        task_instance.update_state(state='STARTED', meta={'progress': 20, 'message': 'Copying targets to axiom-shell container'})
        
        copy_cmd = [
            "docker", "cp", local_targets_file, f"axiom-shell:/home/<USER>/{targets_file}"
        ]
        result = subprocess.run(copy_cmd, capture_output=True, text=True, timeout=30)
        if result.returncode != 0:
            raise Exception(f"Failed to copy targets to axiom-shell: {result.stderr}")

        # Prepare CVE IDs string
        cve_ids_str = ",".join(cve_ids)

        # Run distributed scan
        task_instance.update_state(state='STARTED', meta={'progress': 30, 'message': 'Running distributed nuclei scan'})
        
        scan_cmd = [
            "docker", "exec", "-i", "axiom-shell",
            "ax", "scan", f"/home/<USER>/{targets_file}",
            "-m", "nuclei",
            "-id", cve_ids_str,
            "-oJ", f"/home/<USER>/{temp_dir}/{results_file}"
        ]
        
        result = subprocess.run(scan_cmd, capture_output=True, text=True, timeout=3600)  # 1 hour timeout
        if result.returncode != 0:
            raise Exception(f"Distributed nuclei scan failed: {result.stderr}")

        # Copy results back from container
        task_instance.update_state(state='STARTED', meta={'progress': 70, 'message': 'Retrieving scan results'})
        
        local_results_file = f"/tmp/{results_file}"
        copy_results_cmd = [
            "docker", "cp", f"axiom-shell:/home/<USER>/{temp_dir}/{results_file}", local_results_file
        ]
        result = subprocess.run(copy_results_cmd, capture_output=True, text=True, timeout=60)
        if result.returncode != 0:
            raise Exception(f"Failed to copy results from axiom-shell: {result.stderr}")

        # Process results
        task_instance.update_state(state='STARTED', meta={'progress': 80, 'message': 'Processing scan results'})
        
        return _parse_nuclei_results(local_results_file)

    finally:
        # Clean up temporary files
        try:
            os.unlink(local_targets_file)
            if 'local_results_file' in locals() and os.path.exists(local_results_file):
                os.unlink(local_results_file)
        except Exception as e:
            logger.warning(f"Failed to clean up temporary files: {e}")


def _parse_nuclei_results(results_file: str) -> List[Dict[str, Any]]:
    """Parse nuclei JSONL results file and extract relevant fields"""
    vulnerabilities = []
    seen_combinations = set()  # Track host+template-id combinations to prevent duplicates
    
    # Field extraction order optimized for user experience:
    # 1. Core vulnerability identification (cve-id, name, severity, type)
    # 2. Target information (host, ip, port, url, matched-at)
    # 3. Vulnerability details (description, impact, remediation)
    # 4. Scoring and metrics (cvss-score, cvss-metrics, epss-score, epss-percentile)
    # 5. Technical evidence (matcher-status, extracted-results, timestamp)
    # 6. Technical details (request, response, curl-command)
    
    try:
        if not os.path.exists(results_file):
            logger.warning(f"Results file not found: {results_file}")
            return vulnerabilities
            
        with open(results_file, 'r') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                    
                try:
                    result = json.loads(line)
                    
                    # Extract template-id and matched-at for deduplication check
                    template_id = result.get('template-id', '')
                    matched_at = result.get('matched-at', '')
                    
                    # Create a unique key for this matched-at+template-id combination
                    combination_key = f"{matched_at}::{template_id}"
                    
                    # Skip if we've already seen this combination
                    if combination_key in seen_combinations:
                        logger.debug(f"Skipping duplicate finding for matched-at {matched_at} and template-id {template_id}")
                        continue
                    
                    # Add this combination to our seen set
                    seen_combinations.add(combination_key)
                    
                    # Build vulnerability dict in logical order for better user experience
                    vulnerability = {}
                    info_obj = result.get('info', {})
                    classification_obj = info_obj.get('classification', {}) if info_obj else {}
                    
                    # 1. Core vulnerability identification
                    vulnerability['cve-id'] = result.get('template-id')
                    vulnerability['name'] = info_obj.get('name') if info_obj else None
                    vulnerability['severity'] = info_obj.get('severity') if info_obj else None
                    vulnerability['type'] = result.get('type')
                    
                    # 2. Target information
                    vulnerability['host'] = result.get('host')
                    vulnerability['ip'] = result.get('ip')
                    vulnerability['port'] = result.get('port')
                    vulnerability['url'] = result.get('url')
                    vulnerability['matched-at'] = result.get('matched-at')
                    
                    # 3. Vulnerability details
                    vulnerability['description'] = info_obj.get('description') if info_obj else None
                    vulnerability['impact'] = info_obj.get('impact') if info_obj else None
                    vulnerability['remediation'] = info_obj.get('remediation') if info_obj else None
                    
                    # 4. Scoring and metrics
                    vulnerability['cvss-score'] = classification_obj.get('cvss-score') if classification_obj else None
                    vulnerability['cvss-metrics'] = classification_obj.get('cvss-metrics') if classification_obj else None
                    vulnerability['epss-score'] = classification_obj.get('epss-score') if classification_obj else None
                    vulnerability['epss-percentile'] = classification_obj.get('epss-percentile') if classification_obj else None
                    
                    # 5. Technical evidence
                    vulnerability['matcher-status'] = result.get('matcher-status')
                    extracted_results = result.get('extracted-results')
                    vulnerability['extracted-results'] = extracted_results if extracted_results is not None else []
                    vulnerability['timestamp'] = result.get('timestamp')
                    
                    # 6. Technical details (for debugging/analysis)
                    vulnerability['request'] = result.get('request')
                    vulnerability['response'] = result.get('response')
                    vulnerability['curl-command'] = result.get('curl-command')
                    
                    vulnerabilities.append(vulnerability)
                    
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse nuclei result line: {line}, error: {e}")
                    continue
                    
    except Exception as e:
        logger.error(f"Error parsing nuclei results file {results_file}: {e}")
    
    logger.info(f"Parsed {len(vulnerabilities)} unique vulnerabilities from nuclei results")
    return vulnerabilities 
name: Deploy to EC2 via Bastion

on:
  workflow_dispatch:

jobs:
  build-image:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest
    environment: PROD

    outputs:
      image_tag: ${{ steps.vars.outputs.image_tag }}

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Set lowercase image name
        id: vars
        run: |
          IMAGE_TAG=ghcr.io/$(echo "${{ github.repository_owner }}" | tr '[:upper:]' '[:lower:]')/threatmesh:latest
          echo "image_tag=$IMAGE_TAG" >> $GITHUB_OUTPUT

      - name: Log in to GitHub Container Registry
        run: echo "${{ secrets.GHCR_PAT }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin

      - name: Build Docker image
        run: |
          docker build \
            --build-arg GITHUB_PAT="${{ secrets.GHCR_PAT }}" \
            --build-arg SUBFINDER_PROVIDER="${{ secrets.SUBFINDER_PROVIDER_CREDENTIALS }}" \
            -t ${{ steps.vars.outputs.image_tag }} .

      - name: Push Docker image
        run: docker push ${{ steps.vars.outputs.image_tag }}

  deploy-to-ec2:
    name: Deploy via Bastion
    runs-on: ubuntu-latest
    needs: build-image
    environment: PROD

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Set up SSH access to Bastion
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ secrets.BASTION_HOST }} >> ~/.ssh/known_hosts

      - name: Prepare .env file
        run: |
          cat > .env <<EOF
          GH_USERNAME=${{ github.repository_owner }}
          API_KEY=${{ secrets.API_KEY }}
          DEHASHED_API_KEY=${{ secrets.DEHASHED_API_KEY }}
          DEHASHED_API_URL=${{ secrets.DEHASHED_API_URL }}
          GIT_TOKENS=${{ secrets.GIT_TOKENS }}
          GOOGLE_CSE_API_KEY=${{ secrets.GOOGLE_CSE_API_KEY }}
          MONGODB_DB=${{ secrets.MONGODB_DB }}
          MONGODB_URI=${{ secrets.MONGODB_URI }}
          NVD_CVE_API_KEY=${{ secrets.NVD_CVE_API_KEY }}
          REDIS_URI=${{ secrets.REDIS_URI }}
          EOF

      - name: Copy files to Bastion
        run: |
          scp -i ~/.ssh/id_rsa \
              .env docker-compose.yml \
              ${{ secrets.EC2_USER }}@${{ secrets.BASTION_HOST }}:/tmp/

      - name: SSH into Bastion and deploy to EC2
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no ${{ secrets.EC2_USER }}@${{ secrets.BASTION_HOST }} <<-'EOSSH'
          	set -e

          	echo "Copying files from bastion to private EC2"
          	scp -o StrictHostKeyChecking=no /tmp/.env /tmp/docker-compose.yml \
          	    ${{ secrets.EC2_USER }}@${{ secrets.EC2_HOST }}:/tmp/

          	echo "Deploying on private EC2"
          	ssh -o StrictHostKeyChecking=no ${{ secrets.EC2_USER }}@${{ secrets.EC2_HOST }} <<-'EOF'
          		set -e

          		echo "Logging in to GHCR"
          		echo "${{ secrets.GHCR_PAT }}" | sudo docker login ghcr.io -u ${{ github.actor }} --password-stdin

          		cd /tmp

          		echo "Pulling latest images"
          		sudo docker compose pull

          		echo "Stopping old containers"
          		sudo docker compose down --remove-orphans

          		echo "Starting new containers"
          		sudo docker compose up -d

          		echo "Cleaning up unused Docker resources"
          		sudo docker system prune -af --volumes
          	EOF
          EOSSH
